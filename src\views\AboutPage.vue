<template>
  <div class="min-h-screen bg-white">
    <!-- Navigation -->
    <nav class="fixed top-0 w-full bg-white/95 backdrop-blur-md z-50 shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-20">
          <div class="flex items-center">
            <img src="/src/assets/ordy_logo.svg" alt="Ordy" class="h-12 w-auto">
            <span class="ml-3 text-2xl font-YesevaOne text-gray-900">Ordy</span>
          </div>
          <div class="hidden md:flex items-center space-x-8">
            <router-link to="/new-homepage" class="text-gray-700 hover:text-ordypurple-100 transition-colors font-medium">Startseite</router-link>
            <router-link to="/rezepte" class="text-gray-700 hover:text-ordypurple-100 transition-colors font-medium">Rezepte</router-link>
            <router-link to="/blog" class="text-gray-700 hover:text-ordypurple-100 transition-colors font-medium">Blog</router-link>
            <router-link to="/about" class="text-ordypurple-100 font-medium">Über uns</router-link>
            <router-link to="/login" class="bg-ordypurple-100 text-white px-6 py-3 rounded-full hover:bg-ordypurple-200 transition-all transform hover:scale-105 font-medium shadow-lg">
              Kostenlos starten
            </router-link>
          </div>
        </div>
      </div>
    </nav>

    <!-- Hero Section -->
    <section class="pt-32 pb-16 bg-gradient-to-br from-ordypurple-100 to-ordypurple-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-4xl md:text-6xl font-YesevaOne text-white mb-6">
          Über <span class="text-yellow-300">Ordy</span>
        </h1>
        <p class="text-xl text-white/90 max-w-3xl mx-auto mb-8">
          Wir revolutionieren das Kochen für Familien mit KI-gestützter Technologie, 
          die Lebensmittelverschwendung reduziert und das gemeinsame Kochen zu einem Erlebnis macht.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <router-link to="/login" 
                       class="bg-white text-ordypurple-100 px-8 py-3 rounded-full font-medium hover:bg-gray-50 transition-all">
            Jetzt ausprobieren
          </router-link>
          <a href="#mission" 
             class="border-2 border-white text-white px-8 py-3 rounded-full font-medium hover:bg-white hover:text-ordypurple-100 transition-all">
            Unsere Mission
          </a>
        </div>
      </div>
    </section>

    <!-- Mission Section -->
    <section id="mission" class="py-16">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid lg:grid-cols-2 gap-12 items-center">
          <div>
            <h2 class="text-3xl md:text-4xl font-YesevaOne text-gray-900 mb-6">
              Unsere <span class="text-ordypurple-100">Mission</span>
            </h2>
            <div class="space-y-4 text-lg text-gray-600">
              <p>
                <strong class="text-gray-900">Kochen soll Freude bereiten, nicht stressen.</strong> 
                Deshalb haben wir Ordy entwickelt - eine intelligente Koch-App, die Familien dabei hilft, 
                entspannt und nachhaltig zu kochen.
              </p>
              <p>
                Mit modernster KI-Technologie verwandeln wir Reste in köstliche Mahlzeiten, 
                planen automatisch Wochenpläne und bringen Familien wieder zusammen an den Küchentisch.
              </p>
              <p>
                <strong class="text-green-600">Unser Ziel:</strong> Bis 2025 wollen wir 100.000 Familien dabei helfen, 
                ihre Lebensmittelverschwendung um 50% zu reduzieren und dabei Zeit und Geld zu sparen.
              </p>
            </div>
          </div>
          
          <div class="relative">
            <img src="https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=600&h=400&fit=crop" 
                 alt="Familie beim Kochen" 
                 class="rounded-2xl shadow-2xl">
            <div class="absolute -bottom-6 -left-6 bg-white rounded-2xl p-6 shadow-lg">
              <div class="text-center">
                <div class="text-3xl font-bold text-ordypurple-100">50+</div>
                <div class="text-sm text-gray-600">Tonnen Lebensmittel gerettet</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Values Section -->
    <section class="py-16 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-YesevaOne text-gray-900 mb-4">
            Unsere <span class="text-ordypurple-100">Werte</span>
          </h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            Diese Prinzipien leiten uns bei allem, was wir tun
          </p>
        </div>

        <div class="grid md:grid-cols-3 gap-8">
          <div class="text-center">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg class="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"/>
              </svg>
            </div>
            <h3 class="text-xl font-YesevaOne text-gray-900 mb-4">Nachhaltigkeit</h3>
            <p class="text-gray-600">
              Wir glauben daran, dass Technologie dabei helfen kann, unseren Planeten zu schützen. 
              Jede gerettete Zutat zählt.
            </p>
          </div>

          <div class="text-center">
            <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg class="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
              </svg>
            </div>
            <h3 class="text-xl font-YesevaOne text-gray-900 mb-4">Familie</h3>
            <p class="text-gray-600">
              Kochen verbindet Menschen. Wir entwickeln Technologie, die Familien zusammenbringt, 
              nicht trennt.
            </p>
          </div>

          <div class="text-center">
            <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg class="w-8 h-8 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
              </svg>
            </div>
            <h3 class="text-xl font-YesevaOne text-gray-900 mb-4">Innovation</h3>
            <p class="text-gray-600">
              Wir nutzen modernste KI-Technologie, um alltägliche Probleme zu lösen und 
              das Leben einfacher zu machen.
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Team Section -->
    <section class="py-16">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-YesevaOne text-gray-900 mb-4">
            Unser <span class="text-ordypurple-100">Team</span>
          </h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            Leidenschaftliche Experten aus den Bereichen KI, UX-Design und Nachhaltigkeit
          </p>
        </div>

        <div class="grid md:grid-cols-3 gap-8">
          <div class="text-center">
            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=200&h=200&fit=crop&crop=face" 
                 alt="Dominic Kunz" 
                 class="w-32 h-32 rounded-full mx-auto mb-6 shadow-lg">
            <h3 class="text-xl font-YesevaOne text-gray-900 mb-2">Dominic Kunz</h3>
            <p class="text-ordypurple-100 font-medium mb-3">CEO & Gründer</p>
            <p class="text-gray-600 text-sm">
              Experte für KI und maschinelles Lernen mit über 10 Jahren Erfahrung in der Entwicklung 
              intelligenter Systeme.
            </p>
          </div>

          <div class="text-center">
            <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=200&h=200&fit=crop&crop=face" 
                 alt="Sarah Weber" 
                 class="w-32 h-32 rounded-full mx-auto mb-6 shadow-lg">
            <h3 class="text-xl font-YesevaOne text-gray-900 mb-2">Sarah Weber</h3>
            <p class="text-ordypurple-100 font-medium mb-3">Head of UX Design</p>
            <p class="text-gray-600 text-sm">
              Spezialistin für nutzerzentriertes Design mit Fokus auf Familien-Apps und 
              intuitive Benutzeroberflächen.
            </p>
          </div>

          <div class="text-center">
            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=200&h=200&fit=crop&crop=face" 
                 alt="Michael Schmidt" 
                 class="w-32 h-32 rounded-full mx-auto mb-6 shadow-lg">
            <h3 class="text-xl font-YesevaOne text-gray-900 mb-2">Michael Schmidt</h3>
            <p class="text-ordypurple-100 font-medium mb-3">Head of Sustainability</p>
            <p class="text-gray-600 text-sm">
              Nachhaltigkeitsexperte und Ernährungsberater, der sich für die Reduzierung von 
              Lebensmittelverschwendung einsetzt.
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Technology Section -->
    <section class="py-16 bg-gray-900 text-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-YesevaOne mb-4">
            Modernste <span class="text-ordypurple-100">Technologie</span>
          </h2>
          <p class="text-xl text-gray-300 max-w-3xl mx-auto">
            Erfahre, welche Technologien Ordy so intelligent machen
          </p>
        </div>

        <div class="grid md:grid-cols-2 gap-12 items-center">
          <div>
            <h3 class="text-2xl font-YesevaOne mb-6">KI-gestützte Rezeptgenerierung</h3>
            <div class="space-y-4">
              <div class="flex items-start space-x-3">
                <div class="w-6 h-6 bg-ordypurple-100 rounded-full flex items-center justify-center mt-1">
                  <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                </div>
                <div>
                  <h4 class="font-medium mb-1">OpenAI GPT-4 Integration</h4>
                  <p class="text-gray-300 text-sm">Modernste Sprachmodelle für kreative und präzise Rezeptvorschläge</p>
                </div>
              </div>
              
              <div class="flex items-start space-x-3">
                <div class="w-6 h-6 bg-ordypurple-100 rounded-full flex items-center justify-center mt-1">
                  <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                </div>
                <div>
                  <h4 class="font-medium mb-1">Maschinelles Lernen</h4>
                  <p class="text-gray-300 text-sm">Algorithmen lernen aus Nutzerpräferenzen und verbessern sich kontinuierlich</p>
                </div>
              </div>
              
              <div class="flex items-start space-x-3">
                <div class="w-6 h-6 bg-ordypurple-100 rounded-full flex items-center justify-center mt-1">
                  <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                </div>
                <div>
                  <h4 class="font-medium mb-1">Nährwert-Datenbank</h4>
                  <p class="text-gray-300 text-sm">Umfassende Datenbank für präzise Nährwertberechnungen</p>
                </div>
              </div>
            </div>
          </div>
          
          <div class="relative">
            <div class="bg-gradient-to-br from-ordypurple-100 to-ordypurple-200 rounded-2xl p-8">
              <div class="bg-white rounded-xl p-6">
                <div class="space-y-3">
                  <div class="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div class="h-4 bg-gray-200 rounded w-1/2"></div>
                  <div class="h-4 bg-ordypurple-100 rounded w-2/3"></div>
                  <div class="grid grid-cols-2 gap-2 mt-4">
                    <div class="h-16 bg-gray-100 rounded-lg"></div>
                    <div class="h-16 bg-gray-100 rounded-lg"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Impact Section -->
    <section class="py-16 bg-green-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-YesevaOne text-gray-900 mb-4">
            Unser <span class="text-green-600">Impact</span>
          </h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            Gemeinsam mit unserer Community schaffen wir messbare Veränderungen
          </p>
        </div>

        <div class="grid md:grid-cols-4 gap-8">
          <div class="text-center">
            <div class="text-4xl font-bold text-green-600 mb-2">50+</div>
            <div class="text-gray-900 font-medium mb-1">Tonnen</div>
            <div class="text-gray-600 text-sm">Lebensmittel gerettet</div>
          </div>
          
          <div class="text-center">
            <div class="text-4xl font-bold text-blue-600 mb-2">10.000+</div>
            <div class="text-gray-900 font-medium mb-1">Familien</div>
            <div class="text-gray-600 text-sm">Nutzen Ordy aktiv</div>
          </div>
          
          <div class="text-center">
            <div class="text-4xl font-bold text-purple-600 mb-2">2.5M€</div>
            <div class="text-gray-900 font-medium mb-1">Gespart</div>
            <div class="text-gray-600 text-sm">Durch weniger Verschwendung</div>
          </div>
          
          <div class="text-center">
            <div class="text-4xl font-bold text-red-600 mb-2">15%</div>
            <div class="text-gray-900 font-medium mb-1">CO2-Reduktion</div>
            <div class="text-gray-600 text-sm">Pro Familie im Durchschnitt</div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="py-16 bg-gradient-to-br from-ordypurple-100 to-ordypurple-200">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl md:text-4xl font-YesevaOne text-white mb-6">
          Werde Teil der Ordy-Familie
        </h2>
        <p class="text-xl text-white/90 mb-8">
          Starte noch heute und entdecke, wie entspannt und nachhaltig Kochen sein kann
        </p>
        <router-link to="/login" 
                     class="inline-block bg-white text-ordypurple-100 px-8 py-4 rounded-full text-lg font-medium hover:bg-gray-50 transition-all transform hover:scale-105 shadow-lg">
          Jetzt kostenlos anmelden
        </router-link>
      </div>
    </section>
  </div>
</template>

<script setup>
// Component logic can be added here if needed
</script>

<style scoped>
/* Component-specific styles */
</style>
