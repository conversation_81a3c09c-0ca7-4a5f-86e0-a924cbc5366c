<template>
  <div class="min-h-screen bg-gradient-to-br from-white via-violet-50 to-white">
    <!-- Navigation -->
    <nav class="fixed top-0 w-full bg-white/95 backdrop-blur-md z-50 shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-20">
          <div class="flex items-center">
            <img src="/src/assets/ordy_logo.svg" alt="Ordy" class="h-12 w-auto">
            <span class="ml-3 text-2xl font-YesevaOne text-gray-900">Ordy</span>
          </div>
          <div class="hidden md:flex items-center space-x-8">
            <a href="#features" class="text-gray-700 hover:text-ordypurple-100 transition-colors font-medium">Features</a>
            <a href="#pricing" class="text-gray-700 hover:text-ordypurple-100 transition-colors font-medium">Preise</a>
            <a href="#community" class="text-gray-700 hover:text-ordypurple-100 transition-colors font-medium">Community</a>
            <router-link to="/rezepte" class="text-gray-700 hover:text-ordypurple-100 transition-colors font-medium">Rezepte</router-link>
            <router-link to="/blog" class="text-gray-700 hover:text-ordypurple-100 transition-colors font-medium">Blog</router-link>
            <button @click="startFreeTrial" class="bg-ordypurple-100 text-white px-6 py-3 rounded-full hover:bg-ordypurple-200 transition-all transform hover:scale-105 font-medium shadow-lg">
              Kostenlos starten
            </button>
          </div>
          <button @click="toggleMobileMenu" class="md:hidden p-2">
            <svg class="h-6 w-6 text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>
      </div>

      <!-- Mobile Menu -->
      <div v-if="mobileMenuOpen" class="md:hidden bg-white border-t border-gray-200">
        <div class="px-4 py-6 space-y-4">
          <a href="#features" @click="mobileMenuOpen = false" class="block text-gray-700 hover:text-ordypurple-100 transition-colors font-medium">Features</a>
          <a href="#pricing" @click="mobileMenuOpen = false" class="block text-gray-700 hover:text-ordypurple-100 transition-colors font-medium">Preise</a>
          <a href="#community" @click="mobileMenuOpen = false" class="block text-gray-700 hover:text-ordypurple-100 transition-colors font-medium">Community</a>
          <router-link to="/rezepte" @click="mobileMenuOpen = false" class="block text-gray-700 hover:text-ordypurple-100 transition-colors font-medium">Rezepte</router-link>
          <router-link to="/blog" @click="mobileMenuOpen = false" class="block text-gray-700 hover:text-ordypurple-100 transition-colors font-medium">Blog</router-link>
          <button @click="startFreeTrial" class="w-full bg-ordypurple-100 text-white px-6 py-3 rounded-full hover:bg-ordypurple-200 transition-all font-medium">
            Kostenlos starten
          </button>
        </div>
      </div>
    </nav>

    <!-- Hero Section -->
    <section class="pt-32 pb-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-white via-violet-100 to-white">
      <div class="max-w-7xl mx-auto">
        <div class="grid lg:grid-cols-2 gap-12 items-center">
          <!-- Left Column -->
          <div class="space-y-8">
            <!-- A/B Test Headlines - Improved Typography -->
            <div v-if="headlineVariant === 'emotional'">
              <h1 class="text-[3.8em] md:text-[5em] font-YesevaOne text-gray-900 leading-[3.2rem] md:leading-[6rem] font-normal">
                Kochen war noch nie so <span class="text-ordypurple-100">einfach</span>
              </h1>
              <p class="text-lg md:text-xl text-gray-600 mt-6 font-OpenSans">
                Kreiere und verwalte deine Rezepte <span class="font-bold">spielerisch, magisch</span> und auf die <span class="font-bold">Bedürfnisse deines Haushalts</span> genau zugeschnitten.
              </p>
            </div>

            <div v-else-if="headlineVariant === 'social'" class="space-y-4">
              <div class="flex items-center space-x-2 text-ordypurple-100 font-bold text-sm uppercase">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                <span>Bereits 10.000+ Familien im DACH-Raum vertrauen Ordy</span>
              </div>
              <h1 class="text-[3.8em] md:text-[5em] font-YesevaOne text-gray-900 leading-[3.2rem] md:leading-[6rem] font-normal">
                Die <span class="text-ordypurple-100">intelligenteste</span> Koch-App für Familien
              </h1>
              <p class="text-lg md:text-xl text-gray-600 font-OpenSans">
                KI macht aus deinen Zutaten perfekte Mahlzeiten - wie bei 85% unserer Nutzer.
              </p>
            </div>

            <div v-else class="space-y-4">
              <h1 class="text-[3.8em] md:text-[5em] font-YesevaOne text-gray-900 leading-[3.2rem] md:leading-[6rem] font-normal">
                Schluss mit <span class="text-ordypurple-100">Kochstress</span> und Verschwendung
              </h1>
              <p class="text-lg md:text-xl text-gray-600 font-OpenSans">
                Mit Ordy sparst du Zeit, Geld und reduzierst Lebensmittelverschwendung um 40%.
              </p>
            </div>

            <!-- Social Proof Bar -->
            <div class="flex flex-wrap items-center gap-6 text-sm text-gray-600">
              <div class="flex items-center space-x-2">
                <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                <span>10.000+ aktive Familien im DACH-Raum</span>
              </div>
              <div class="flex items-center space-x-2">
                <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                <span>85% weniger Lebensmittelverschwendung</span>
              </div>
              <div class="flex items-center space-x-2">
                <svg class="w-4 h-4 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                </svg>
                <span>4.8★ Bewertung</span>
              </div>
            </div>

            <!-- CTA Buttons -->
            <div class="flex flex-col md:flex-row gap-4 mt-8">
              <button @click="scrollToDemo"
                      class="bg-white border-3 px-6 py-3 rounded-xl text-black uppercase font-semibold shadow-lg hover:bg-gray-25 transition-all">
                über die app
              </button>
              <button @click="startFreeTrial"
                      class="shiny-purple-button bg-ordypurple-100 px-6 py-4 rounded-xl text-white uppercase font-semibold shadow-lg hover:bg-ordypurple-200 transition-all">
                Familie anmelden
              </button>
            </div>

            <!-- Trust Indicators -->
            <div class="text-sm text-gray-500 mt-4">
              <p>✓ Sofort nutzbar ✓ Keine Kreditkarte ✓ Jederzeit kündbar</p>
            </div>
          </div>

          <!-- Right Column - Berner Rösti Card -->
          <div class="w-full max-w-sm mx-auto md:p-0 p-6 mt-8 md:mt-0 z-10 flex flex-row relative">
            <!-- Animierte Berner Rösti Karte -->
            <div class="menu-card-copy relative overflow-hidden rounded-xl bg-cover bg-center w-full flex flex-col justify-between antialiased"
                 :style="{
                     backgroundImage: `url(${bernerRoestiImg})`,
                     aspectRatio: '2/3',
                     height: '380px',
                     maxHeight: '380px'
                 }"
                 :class="{ 'card-animations-active': startCardAnimations }">
                <!-- Inhalt direkt hier -->
                <!-- head -->
                <div class="w-full mt-2 p-4 md:p-6 flex flex-row text-right text-white">
                    <div class="w-4/5 pr-2 md:pr-4">
                        <h3 class="title-element mt-1 text-sm md:text-base relative font-semibold leading-tight" style="text-shadow: 1px 1px 3px rgba(0,0,0,0.7);">{{ displayedTitle }}</h3>
                    </div>
                    <div class="w-1/5">
                        <button class="mt-2 w-14 h-14 bg-white bg-opacity-80 hover:bg-opacity-100 rounded-lg flex items-center justify-center cursor-pointer transition-all duration-150 reload-icon-container">
                            <img :src="reloadIcon" class="mx-auto h-9 w-9 md:h-6 md:w-6 reload-icon-img" alt="reload" />
                        </button>
                    </div>
                </div>
                <!-- foot -->
                <div class="absolute bottom-0 left-0 right-0 p-4 md:p-6 flex flex-nowrap gap-2 md:gap-4">
                    <div class="footer-button-1 w-5/12 opacity-0">
                        <div class="h-9 w-full bg-white bg-opacity-90 rounded-lg flex flex-row justify-center items-center px-1 shadow-sm border border-gray-200 text-black">
                            <span class="text-xs md:text-sm">4</span>
                            <img class="h-5 md:h-6 mx-1" :src="peopleIcon" alt="people" />
                            <span class="break-keep text-xs md:text-sm">25 min</span>
                        </div>
                    </div>
                    <div class="footer-button-2 w-5/12 opacity-0">
                        <button class="h-9 bg-white bg-opacity-90 hover:bg-opacity-100 rounded-lg w-full text-xs md:text-sm shadow-sm border border-gray-200 text-ordypurple-700 cursor-pointer transition-all duration-150">
                            <span class="button-typing-text">zum Rezept</span>
                        </button>
                    </div>
                    <div class="footer-button-3 w-2/12 mx-auto h-auto opacity-0">
                        <button class="h-9 w-9 float-right rounded-full transition-all duration-150 focus:outline-none bg-white bg-opacity-90 hover:bg-opacity-100 shadow-sm border border-gray-200">
                            <img :src="addIcon" class="mx-auto h-5 w-5" alt="add" />
                        </button>
                    </div>
                </div>
            </div>

            <!-- Floating Success Metrics -->
            <div class="absolute -top-3 -right-6 bg-white rounded-xl p-3 shadow-lg border border-gray-100 z-20">
              <div class="text-center">
                <div class="text-lg font-bold text-ordypurple-100" ref="savedTonsCounter">{{ animatedSavedTons }}</div>
                <div class="text-xs text-gray-600">Tonnen gerettet</div>
              </div>
            </div>

            <div class="absolute -bottom-3 -left-6 bg-white rounded-xl p-3 shadow-lg border border-gray-100 z-20">
              <div class="text-center">
                <div class="text-lg font-bold text-green-500">{{ animatedSavedMoney }} {{ currencySymbol }}</div>
                <div class="text-xs text-gray-600">Gespart</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Progressive Disclosure Section -->
    <section id="features" class="py-16 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <span class="font-bold text-ordypurple-100 text-center uppercase text-sm">FÜR FREUNDE UND FAMILIE</span>
          <h2 class="text-[3em] md:text-[3em] font-YesevaOne text-gray-900 leading-[3rem] md:leading-[6rem] pt-4 pb-4 mb-4">
            So einfach funktioniert <span class="text-ordypurple-100">Ordy</span>
          </h2>
          <p class="text-base md:text-lg text-gray-600 max-w-3xl mx-auto">
            In nur 3 Schritten von Resten zu perfekten Mahlzeiten - wie bereits 10.000+ Familien im DACH-Raum erleben
          </p>
        </div>

        <div class="grid md:grid-cols-3 gap-8">
          <!-- Step 1 -->
          <div class="text-center group cursor-pointer" @click="activeStep = 1">
            <div class="relative mb-6">
              <div class="w-24 h-24 bg-gradient-to-br from-ordypurple-100 to-ordypurple-200 rounded-full flex items-center justify-center mx-auto group-hover:scale-110 transition-transform">
                <span class="text-2xl font-bold text-white">1</span>
              </div>
              <div class="absolute -top-2 -right-2 w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
            </div>
            <h3 class="text-lg md:text-xl font-YesevaOne text-gray-900 mb-2">Zutaten eingeben</h3>
            <p class="text-sm md:text-base text-gray-600 mb-4">Was ist in deinem Kühlschrank? Einfach eingeben oder Foto machen.</p>
            <div class="text-xs md:text-sm text-ordypurple-100 font-medium">In 30 Sekunden zur Rezeptidee</div>
          </div>

          <!-- Step 2 -->
          <div class="text-center group cursor-pointer" @click="activeStep = 2">
            <div class="relative mb-6">
              <div class="w-24 h-24 bg-gradient-to-br from-ordypurple-100 to-ordypurple-200 rounded-full flex items-center justify-center mx-auto group-hover:scale-110 transition-transform">
                <span class="text-2xl font-bold text-white">2</span>
              </div>
              <div class="absolute -top-2 -right-2 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                </svg>
              </div>
            </div>
            <h3 class="text-lg md:text-xl font-YesevaOne text-gray-900 mb-2">KI erstellt Rezepte</h3>
            <p class="text-sm md:text-base text-gray-600 mb-4">Unsere KI generiert perfekte Rezepte - angepasst an deine Familie.</p>
            <div class="text-xs md:text-sm text-ordypurple-100 font-medium">Wie 8.500 andere Familien heute</div>
          </div>

          <!-- Step 3 -->
          <div class="text-center group cursor-pointer" @click="activeStep = 3">
            <div class="relative mb-6">
              <div class="w-24 h-24 bg-gradient-to-br from-ordypurple-100 to-ordypurple-200 rounded-full flex items-center justify-center mx-auto group-hover:scale-110 transition-transform">
                <span class="text-2xl font-bold text-white">3</span>
              </div>
              <div class="absolute -top-2 -right-2 w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                </svg>
              </div>
            </div>
            <h3 class="text-lg md:text-xl font-YesevaOne text-gray-900 mb-2">Gemeinsam genießen</h3>
            <p class="text-sm md:text-base text-gray-600 mb-4">Familie plant und kocht zusammen - mehr Zeit für das, was zählt.</p>
            <div class="text-xs md:text-sm text-ordypurple-100 font-medium">40% weniger Verschwendung</div>
          </div>
        </div>

        <!-- Interactive Demo Preview -->
        <div class="mt-16 bg-gray-50 rounded-3xl p-8">
          <div class="text-center mb-8">
            <h3 class="text-xl md:text-2xl font-YesevaOne text-gray-900 mb-2">Probiere Ordy jetzt aus</h3>
            <p class="text-sm md:text-base text-gray-600">Ohne Anmeldung - sieh sofort, wie es funktioniert</p>
          </div>
          
          <div class="max-w-2xl mx-auto">
            <div class="bg-white rounded-2xl p-6 shadow-lg">
              <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Welche Zutaten hast du da?</label>
                <input v-model="demoIngredients" 
                       type="text" 
                       placeholder="z.B. Eier, Spinat, Käse, Tomaten"
                       class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-ordypurple-100 focus:border-transparent">
              </div>
              
              <div class="flex flex-wrap gap-2 mb-4">
                <button v-for="ingredient in suggestedIngredients" 
                        :key="ingredient"
                        @click="addIngredient(ingredient)"
                        class="px-3 py-1 bg-ordypurple-100 text-white rounded-full text-sm hover:bg-ordypurple-200 transition-colors">
                  {{ ingredient }}
                </button>
              </div>
              
              <button @click="generateDemoRecipe" 
                      :disabled="!demoIngredients || isGenerating"
                      class="w-full bg-ordypurple-100 text-white py-3 rounded-xl font-medium hover:bg-ordypurple-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                {{ isGenerating ? 'Rezept wird generiert...' : 'Rezept generieren' }}
              </button>
              
              <!-- Demo Results -->
              <div v-if="demoRecipes.length > 0" class="mt-6 space-y-4">
                <h4 class="font-medium text-gray-900">Deine personalisierten Rezeptvorschläge:</h4>
                <div v-for="recipe in demoRecipes" :key="recipe.id" class="border border-gray-200 rounded-xl p-4">
                  <h5 class="font-YesevaOne text-lg text-gray-900">{{ recipe.name }}</h5>
                  <p class="text-gray-600 text-sm mt-1">{{ recipe.description }}</p>
                  <div class="flex items-center justify-between mt-3">
                    <span class="text-sm text-gray-500">⏱️ {{ recipe.time }} Min</span>
                    <button class="text-ordypurple-100 hover:text-ordypurple-200 text-sm font-medium">
                      Vollständiges Rezept →
                    </button>
                  </div>
                </div>
                <div class="text-center pt-4">
                  <button @click="startFreeTrial" class="bg-ordypurple-100 text-white px-6 py-2 rounded-full hover:bg-ordypurple-200 transition-colors">
                    Wow! Das war einfach. Jetzt kostenlos anmelden
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Social Proof Section -->
    <section class="py-16 bg-gradient-to-br from-ordypurple-100 to-ordypurple-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-5xl font-YesevaOne text-white mb-4">
            Echte Familien, echte Erfolge
          </h2>
          <p class="text-xl text-white/90 max-w-3xl mx-auto">
            Über 10.000 Familien im DACH-Raum haben bereits ihr Kochleben transformiert
          </p>
        </div>

        <!-- Success Stories -->
        <div class="grid md:grid-cols-3 gap-8 mb-16">
          <div class="bg-white rounded-2xl p-6 shadow-lg">
            <div class="flex items-center mb-4">
              <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=60&h=60&fit=crop&crop=face"
                   alt="Familie Müller" class="w-12 h-12 rounded-full">
              <div class="ml-3">
                <h4 class="font-medium text-gray-900">Familie Müller</h4>
                <div class="flex text-yellow-400">
                  <svg v-for="i in 5" :key="i" class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                  </svg>
                </div>
              </div>
            </div>
            <p class="text-gray-600 italic mb-4">
              "Dank Ordy haben wir {{ getLocalizedAmount(200) }} {{ currencySymbol }} im Monat gespart und kochen wieder gerne zusammen. Die KI-Vorschläge sind genial!"
            </p>
            <div class="text-sm text-ordypurple-100 font-medium">
              Spart {{ getLocalizedAmount(200) }} {{ currencySymbol }}/Monat • Familie mit 4 Personen
            </div>
          </div>

          <div class="bg-white rounded-2xl p-6 shadow-lg">
            <div class="flex items-center mb-4">
              <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=60&h=60&fit=crop&crop=face"
                   alt="Thomas K." class="w-12 h-12 rounded-full">
              <div class="ml-3">
                <h4 class="font-medium text-gray-900">Thomas K.</h4>
                <div class="flex text-yellow-400">
                  <svg v-for="i in 5" :key="i" class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                  </svg>
                </div>
              </div>
            </div>
            <p class="text-gray-600 italic mb-4">
              "Als berufstätiger Vater war Kochplanung immer Stress. Ordy plant meine Woche - ich komme entspannt nach Hause."
            </p>
            <div class="text-sm text-ordypurple-100 font-medium">
              3 Stunden/Woche gespart • Berufstätiger Vater
            </div>
          </div>

          <div class="bg-white rounded-2xl p-6 shadow-lg">
            <div class="flex items-center mb-4">
              <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=60&h=60&fit=crop&crop=face"
                   alt="Lisa B." class="w-12 h-12 rounded-full">
              <div class="ml-3">
                <h4 class="font-medium text-gray-900">Lisa B.</h4>
                <div class="flex text-yellow-400">
                  <svg v-for="i in 5" :key="i" class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                  </svg>
                </div>
              </div>
            </div>
            <p class="text-gray-600 italic mb-4">
              "Unsere WG wirft seit Ordy praktisch keine Lebensmittel mehr weg. Aus Nudeln und Gemüseresten wird immer was Leckeres!"
            </p>
            <div class="text-sm text-ordypurple-100 font-medium">
              90% weniger Food Waste • Studenten-WG
            </div>
          </div>
        </div>

        <!-- Quantified Impact -->
        <div class="bg-white/10 backdrop-blur-sm rounded-3xl p-8">
          <div class="text-center mb-8">
            <h3 class="text-2xl font-YesevaOne text-white mb-2">Die Ordy-Community hat bereits erreicht:</h3>
          </div>

          <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div class="text-center">
              <div class="text-4xl font-bold text-white mb-2">{{ animatedSavedTons }}</div>
              <div class="text-white/80">Tonnen Lebensmittel gerettet</div>
            </div>
            <div class="text-center">
              <div class="text-4xl font-bold text-white mb-2">2.5M {{ currencySymbol }}</div>
              <div class="text-white/80">Haushaltsbudget gespart</div>
            </div>
            <div class="text-center">
              <div class="text-4xl font-bold text-white mb-2">125k</div>
              <div class="text-white/80">Stunden Planungszeit eingespart</div>
            </div>
            <div class="text-center">
              <div class="text-4xl font-bold text-white mb-2">15k</div>
              <div class="text-white/80">Neue Lieblingsrezepte entdeckt</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="py-16 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-5xl font-YesevaOne text-gray-900 mb-4">
            Wähle deinen <span class="text-ordypurple-100">Familienplan</span>
          </h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            Durchschnittliche Ersparnis durch weniger Verschwendung: Basic-Nutzer sparen {{ getLocalizedAmount(45) }} {{ currencySymbol }}/Monat - der Plan kostet nur {{ getLocalizedAmount(6) }} {{ currencySymbol }}
          </p>
        </div>

        <div class="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
          <!-- Free Plan -->
          <div class="border border-gray-200 rounded-3xl p-8 relative">
            <div class="text-center mb-8">
              <h3 class="text-2xl font-YesevaOne text-gray-900 mb-2">Starter</h3>
              <p class="text-gray-600 mb-4">Perfekt zum Ausprobieren</p>
              <div class="text-4xl font-bold text-gray-900 mb-2">Kostenlos</div>
              <p class="text-sm text-gray-500">Wie 60% unserer Neukunden starten</p>
            </div>

            <ul class="space-y-3 mb-8">
              <li class="flex items-center">
                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                <span>5 KI-Rezepte</span>
              </li>
              <li class="flex items-center">
                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                <span>8 Kochassistent-Fragen</span>
              </li>
              <li class="flex items-center">
                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                <span>3 Rezept-Uploads</span>
              </li>
              <li class="flex items-center">
                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                <span>Basis Einkaufslisten</span>
              </li>
            </ul>

            <button @click="startFreeTrial" class="w-full border-2 border-ordypurple-100 text-ordypurple-100 py-3 rounded-xl font-medium hover:bg-ordypurple-100 hover:text-white transition-all">
              Kostenlos starten
            </button>
          </div>

          <!-- Basic Plan - Popular -->
          <div class="border-2 border-ordypurple-100 rounded-3xl p-8 relative bg-gradient-to-br from-ordypurple-100/5 to-ordypurple-200/5">
            <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
              <span class="bg-ordypurple-100 text-white px-6 py-2 rounded-full text-sm font-medium">⭐ BELIEBT</span>
            </div>

            <div class="text-center mb-8">
              <h3 class="text-2xl font-YesevaOne text-gray-900 mb-2">Familie</h3>
              <p class="text-gray-600 mb-4">Für aktive Familien</p>
              <div class="text-4xl font-bold text-gray-900 mb-2">
                {{ getLocalizedAmount(6) }} {{ currencySymbol }}<span class="text-lg text-gray-500">/Monat</span>
              </div>
              <p class="text-sm text-ordypurple-100 font-medium">85% unserer Familien im DACH-Raum wählen diesen Plan</p>
              <p class="text-xs text-gray-500 line-through">statt {{ getLocalizedAmount(12) }} {{ currencySymbol }} einzeln</p>
            </div>

            <ul class="space-y-3 mb-8">
              <li class="flex items-center">
                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                <span>23 KI-Rezepte monatlich</span>
              </li>
              <li class="flex items-center">
                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                <span>Familien-Kollaboration</span>
              </li>
              <li class="flex items-center">
                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                <span>Unbegrenzte Einkaufslisten</span>
              </li>
              <li class="flex items-center">
                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                <span>Wochenplanung</span>
              </li>
              <li class="flex items-center">
                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                <span>25 Sprach-Assistent</span>
              </li>
            </ul>

            <button @click="startFreeTrial" class="w-full bg-ordypurple-100 text-white py-3 rounded-xl font-medium hover:bg-ordypurple-200 transition-all transform hover:scale-105">
              Familie anmelden
            </button>

            <div class="mt-4 text-center">
              <p class="text-sm text-green-600 font-medium">💡 Ersparnis: {{ getLocalizedAmount(45) }} {{ currencySymbol }}/Monat durch weniger Verschwendung</p>
            </div>
          </div>

          <!-- Advanced Plan -->
          <div class="border border-gray-200 rounded-3xl p-8 relative">
            <div class="text-center mb-8">
              <h3 class="text-2xl font-YesevaOne text-gray-900 mb-2">Power-Familie</h3>
              <p class="text-gray-600 mb-4">Für Kochbegeisterte</p>
              <div class="text-4xl font-bold text-gray-900 mb-2">
                {{ getLocalizedAmount(17) }} {{ currencySymbol }}<span class="text-lg text-gray-500">/Monat</span>
              </div>
              <p class="text-sm text-gray-500">Für Familien, die täglich kochen</p>
            </div>

            <ul class="space-y-3 mb-8">
              <li class="flex items-center">
                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                <span>100 KI-Rezepte</span>
              </li>
              <li class="flex items-center">
                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                <span>Premium-Support</span>
              </li>
              <li class="flex items-center">
                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                <span>Früher Zugang zu Features</span>
              </li>
              <li class="flex items-center">
                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                <span>100 Sprach-Assistent</span>
              </li>
              <li class="flex items-center">
                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                <span>Unbegrenzte Kollaboration</span>
              </li>
            </ul>

            <button @click="startFreeTrial" class="w-full border-2 border-ordypurple-100 text-ordypurple-100 py-3 rounded-xl font-medium hover:bg-ordypurple-100 hover:text-white transition-all">
              Upgrade wählen
            </button>
          </div>
        </div>
      </div>
    </section>

    <!-- Community Section -->
    <CommunitySection @join-community="startFreeTrial" />

    <!-- Sustainability Section -->
    <SustainabilitySection @start-saving="startFreeTrial" />

    <!-- PWA Section -->
    <section class="py-16 bg-gray-900 text-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid lg:grid-cols-2 gap-12 items-center">
          <div>
            <h2 class="text-3xl md:text-5xl font-YesevaOne mb-6">
              Keine App nötig - funktioniert <span class="text-ordypurple-100">überall</span>
            </h2>
            <div class="space-y-4 mb-8">
              <div class="flex items-center space-x-3">
                <svg class="w-6 h-6 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                <span class="text-lg">Sofort nutzbar auf allen Geräten</span>
              </div>
              <div class="flex items-center space-x-3">
                <svg class="w-6 h-6 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                <span class="text-lg">Offline-Kochen ohne Internet</span>
              </div>
              <div class="flex items-center space-x-3">
                <svg class="w-6 h-6 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                <span class="text-lg">Automatische Updates</span>
              </div>
              <div class="flex items-center space-x-3">
                <svg class="w-6 h-6 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                <span class="text-lg">Kein App Store Download</span>
              </div>
            </div>
            <p class="text-xl text-gray-300 mb-8">
              Einfach ordyapp.com öffnen und loslegen - auf iPhone, Android, Tablet oder Desktop
            </p>
            <button @click="startFreeTrial"
                    class="bg-ordypurple-100 text-white px-8 py-4 rounded-full text-lg font-medium hover:bg-ordypurple-200 transition-all transform hover:scale-105">
              Jetzt ausprobieren
            </button>
          </div>

          <div class="relative">
            <!-- Device Mockups -->
            <div class="relative">
              <!-- Phone Mockup -->
              <div class="relative z-10 mx-auto w-64">
                <div class="bg-gray-800 rounded-3xl p-2 shadow-2xl">
                  <div class="bg-white rounded-2xl overflow-hidden">
                    <div class="h-6 bg-gray-100 flex items-center justify-center">
                      <div class="w-16 h-1 bg-gray-400 rounded-full"></div>
                    </div>
                    <div class="p-4 space-y-3">
                      <div class="h-8 bg-ordypurple-100 rounded-lg"></div>
                      <div class="space-y-2">
                        <div class="h-4 bg-gray-200 rounded w-3/4"></div>
                        <div class="h-4 bg-gray-200 rounded w-1/2"></div>
                      </div>
                      <div class="grid grid-cols-2 gap-2">
                        <div class="h-16 bg-gray-100 rounded-lg"></div>
                        <div class="h-16 bg-gray-100 rounded-lg"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Tablet Mockup -->
              <div class="absolute -top-8 -right-8 w-48 opacity-75">
                <div class="bg-gray-800 rounded-2xl p-2 shadow-xl">
                  <div class="bg-white rounded-xl overflow-hidden">
                    <div class="p-3 space-y-2">
                      <div class="h-6 bg-ordypurple-100 rounded"></div>
                      <div class="grid grid-cols-3 gap-1">
                        <div class="h-12 bg-gray-100 rounded"></div>
                        <div class="h-12 bg-gray-100 rounded"></div>
                        <div class="h-12 bg-gray-100 rounded"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Desktop Mockup -->
              <div class="absolute -bottom-8 -left-8 w-56 opacity-75">
                <div class="bg-gray-800 rounded-lg p-1 shadow-xl">
                  <div class="bg-white rounded overflow-hidden">
                    <div class="h-2 bg-gray-100 flex items-center px-1">
                      <div class="flex space-x-1">
                        <div class="w-1 h-1 bg-red-400 rounded-full"></div>
                        <div class="w-1 h-1 bg-yellow-400 rounded-full"></div>
                        <div class="w-1 h-1 bg-green-400 rounded-full"></div>
                      </div>
                    </div>
                    <div class="p-2 space-y-1">
                      <div class="h-3 bg-ordypurple-100 rounded"></div>
                      <div class="grid grid-cols-4 gap-1">
                        <div class="h-8 bg-gray-100 rounded"></div>
                        <div class="h-8 bg-gray-100 rounded"></div>
                        <div class="h-8 bg-gray-100 rounded"></div>
                        <div class="h-8 bg-gray-100 rounded"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Trust & Security Section -->
    <section class="py-16 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-5xl font-YesevaOne text-gray-900 mb-4">
            Deine Daten sind <span class="text-ordypurple-100">sicher</span>
          </h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            Über 10.000 Familien im DACH-Raum vertrauen uns bereits - höchste Sicherheitsstandards und europäischer Datenschutz
          </p>
        </div>

        <div class="grid md:grid-cols-5 gap-8 items-center">
          <div class="text-center">
            <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"/>
              </svg>
            </div>
            <h3 class="font-YesevaOne text-gray-900 mb-2">DSGVO-konform</h3>
            <p class="text-gray-600 text-sm">EU-Server & deutscher Datenschutz</p>
          </div>

          <div class="text-center">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <h3 class="font-YesevaOne text-gray-900 mb-2">Ausgezeichnet</h3>
            <p class="text-gray-600 text-sm">Von Experten empfohlen</p>
          </div>

          <div class="text-center">
            <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"/>
              </svg>
            </div>
            <h3 class="font-YesevaOne text-gray-900 mb-2">DACH-Unternehmen</h3>
            <p class="text-gray-600 text-sm">Lokaler Support & Service</p>
          </div>

          <div class="text-center">
            <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z"/>
                <path fill-rule="evenodd" d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" clip-rule="evenodd"/>
              </svg>
            </div>
            <h3 class="font-YesevaOne text-gray-900 mb-2">Sichere Zahlung</h3>
            <p class="text-gray-600 text-sm">Stripe-verschlüsselt</p>
          </div>

          <div class="text-center">
            <div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
              </svg>
            </div>
            <h3 class="font-YesevaOne text-gray-900 mb-2">Deutscher Support</h3>
            <p class="text-gray-600 text-sm">Persönliche Betreuung</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Final CTA Section -->
    <section class="py-16 bg-gradient-to-br from-ordypurple-100 to-ordypurple-200">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl md:text-5xl font-YesevaOne text-white mb-6">
          Starte noch heute - wie {{ todaySignups }} andere Familien diese Woche
        </h2>
        <p class="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
          Werde Teil der Ordy-Familie und entdecke, wie entspannt Kochen sein kann
        </p>

        <div class="flex flex-col sm:flex-row gap-4 justify-center mb-8">
          <button @click="startFreeTrial"
                  class="bg-white text-ordypurple-100 px-8 py-4 rounded-full text-lg font-medium hover:bg-gray-50 transition-all transform hover:scale-105 shadow-lg">
            Jetzt kostenlos Familie anmelden
          </button>
          <button @click="scrollToDemo"
                  class="border-2 border-white text-white px-8 py-4 rounded-full text-lg font-medium hover:bg-white hover:text-ordypurple-100 transition-all">
            Noch einmal Demo ansehen
          </button>
        </div>

        <div class="text-white/80 text-sm mb-8">
          ✓ Sofort nutzbar ✓ Keine Kreditkarte ✓ Jederzeit kündbar
        </div>

        <!-- Rotating Testimonials -->
        <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 max-w-2xl mx-auto">
          <div class="transition-all duration-500" :key="currentTestimonial">
            <p class="text-white italic text-lg mb-4">
              "{{ testimonials[currentTestimonial].text }}"
            </p>
            <div class="text-white/80 font-medium">
              - {{ testimonials[currentTestimonial].author }}
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid md:grid-cols-4 gap-8">
          <div>
            <div class="flex items-center mb-4">
              <img src="/src/assets/ordy_logo.svg" alt="Ordy" class="h-8 w-auto">
              <span class="ml-2 text-xl font-YesevaOne">Ordy</span>
            </div>
            <p class="text-gray-400 mb-4">
              Die intelligenteste Koch-App für Familien. Kochen war noch nie so einfach.
            </p>
            <div class="flex space-x-4">
              <span class="text-gray-500 text-sm">Folge uns bald auf Social Media!</span>
            </div>
          </div>

          <div>
            <h3 class="font-YesevaOne text-lg mb-4">Produkt</h3>
            <ul class="space-y-2 text-gray-400">
              <li><a href="#features" class="hover:text-white transition-colors">Features</a></li>
              <li><a href="#pricing" class="hover:text-white transition-colors">Preise</a></li>
              <li><router-link to="/rezepte" class="hover:text-white transition-colors">Rezepte</router-link></li>
              <li><a href="#community" class="hover:text-white transition-colors">Community</a></li>
            </ul>
          </div>

          <div>
            <h3 class="font-YesevaOne text-lg mb-4">Unternehmen</h3>
            <ul class="space-y-2 text-gray-400">
              <li><router-link to="/about" class="hover:text-white transition-colors">Über uns</router-link></li>
              <li><router-link to="/blog" class="hover:text-white transition-colors">Blog</router-link></li>
              <li><a href="#" class="hover:text-white transition-colors opacity-50 cursor-not-allowed">Karriere</a></li>
              <li><a href="#" class="hover:text-white transition-colors opacity-50 cursor-not-allowed">Presse</a></li>
            </ul>
          </div>

          <div>
            <h3 class="font-YesevaOne text-lg mb-4">Support</h3>
            <ul class="space-y-2 text-gray-400">
              <li><a href="#" class="hover:text-white transition-colors opacity-50 cursor-not-allowed">Hilfe</a></li>
              <li><a href="#" class="hover:text-white transition-colors opacity-50 cursor-not-allowed">Kontakt</a></li>
              <li><router-link to="/legal/agb" class="hover:text-white transition-colors">AGB</router-link></li>
              <li><router-link to="/legal/privacy" class="hover:text-white transition-colors">Datenschutz</router-link></li>
            </ul>
          </div>
        </div>

        <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
          <p>&copy; 2024 K-Innovations GmbH. Alle Rechte vorbehalten.</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import CommunitySection from '../components/CommunitySection.vue'
import SustainabilitySection from '../components/SustainabilitySection.vue'

// Import assets for Berner Rösti card
import reloadIcon from '/src/assets/icons/reload.png'
import peopleIcon from '/src/assets/icons/people.png'
import addIcon from '/src/assets/icons/add.png'
import bernerRoestiImg from '/src/assets/img/_bernerroesti.png'

const router = useRouter()

// A/B Testing variants
const headlineVariant = ref('emotional') // 'emotional', 'social', 'problem'
const ctaVariant = ref('start') // 'start', 'family', 'test'

// Mobile menu
const mobileMenuOpen = ref(false)

// Animation counters
const animatedSavedTons = ref(0)
const animatedSavedMoney = ref(0)

// Interactive demo
const demoIngredients = ref('')
const isGenerating = ref(false)
const demoRecipes = ref([])
const activeStep = ref(1)

const suggestedIngredients = ['Eier', 'Spinat', 'Käse', 'Tomaten', 'Nudeln', 'Hähnchen', 'Zwiebeln', 'Paprika']

// Additional state
const todaySignups = ref(127)
const currentTestimonial = ref(0)
const testimonialInterval = ref(null)

// Berner Rösti Card state
const startCardAnimations = ref(false)
const displayedTitle = ref('')
const targetTitle = ref('Berner Rösti mit Käse und Ei')
let typingInterval = null
let typingCursorInterval = null

// DACH Region & Currency Detection
const userRegion = ref('DACH')
const currency = ref('EUR/CHF')
const currencySymbol = ref('€/CHF')



// Detect user region (simplified - in production use proper geolocation)
const detectRegion = () => {
  const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone
  const locale = navigator.language || 'de-DE'

  if (timezone.includes('Zurich') || locale.includes('CH')) {
    currency.value = 'CHF'
    currencySymbol.value = 'CHF'
    userRegion.value = 'CH'
  } else if (timezone.includes('Vienna') || locale.includes('AT')) {
    currency.value = 'EUR'
    currencySymbol.value = '€'
    userRegion.value = 'AT'
  } else {
    currency.value = 'EUR'
    currencySymbol.value = '€'
    userRegion.value = 'DE'
  }
}

const testimonials = [
  {
    text: "Beste Entscheidung für unsere Familie! Wir sparen Zeit und Geld.",
    author: "Sarah M."
  },
  {
    text: "Endlich entspannt kochen! Die KI-Vorschläge sind genial.",
    author: "Thomas K."
  },
  {
    text: "Nie wieder Reste wegwerfen! Ordy macht aus allem was Leckeres.",
    author: "Lisa B."
  },
  {
    text: "Unsere Kinder lieben es, beim Planen zu helfen. Kochen ist wieder Familienzeit.",
    author: "Familie Müller"
  }
]

// Methods
const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value
}

const scrollToDemo = () => {
  document.getElementById('features').scrollIntoView({ behavior: 'smooth' })
}

const startFreeTrial = () => {
  // Track conversion event
  console.log('Free trial started', { headlineVariant: headlineVariant.value, ctaVariant: ctaVariant.value })
  router.push('/login')
}

const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value
}

const addIngredient = (ingredient) => {
  if (!demoIngredients.value.includes(ingredient)) {
    demoIngredients.value = demoIngredients.value ? `${demoIngredients.value}, ${ingredient}` : ingredient
  }
}

const generateDemoRecipe = async () => {
  isGenerating.value = true
  
  // Simulate API call
  await new Promise(resolve => setTimeout(resolve, 2000))
  
  demoRecipes.value = [
    {
      id: 1,
      name: 'Cremige Spinat-Käse-Omelette',
      description: 'Perfekt für ein schnelles Abendessen mit den Zutaten, die du da hast.',
      time: 15
    },
    {
      id: 2,
      name: 'Mediterrane Gemüse-Pfanne',
      description: 'Gesund, lecker und in nur einer Pfanne zubereitet.',
      time: 20
    },
    {
      id: 3,
      name: 'Käse-Spinat-Quesadillas',
      description: 'Knusprig, käsig und bei Kindern sehr beliebt.',
      time: 12
    }
  ]
  
  isGenerating.value = false
}

// Rotate testimonials
const rotateTestimonials = () => {
  currentTestimonial.value = (currentTestimonial.value + 1) % testimonials.length
}

// Currency conversion (simplified - in production use real exchange rates)
const getLocalizedAmount = (euroAmount) => {
  if (currency.value === 'CHF') {
    return Math.round(euroAmount * 1.1) // Approximate EUR to CHF conversion
  }
  return euroAmount
}

// Animate counters on mount
onMounted(() => {
  // Detect user region and set currency
  detectRegion()

  // Animate saved tons counter
  const targetTons = 50
  const tonsInterval = setInterval(() => {
    if (animatedSavedTons.value < targetTons) {
      animatedSavedTons.value += 1
    } else {
      clearInterval(tonsInterval)
    }
  }, 50)

  // Animate saved money counter
  const targetMoney = 180
  const moneyInterval = setInterval(() => {
    if (animatedSavedMoney.value < targetMoney) {
      animatedSavedMoney.value += 5
    } else {
      clearInterval(moneyInterval)
    }
  }, 30)

  // Set random A/B test variants (in production, this would be more sophisticated)
  const variants = ['emotional', 'social', 'problem']
  const ctaVariants = ['start', 'family', 'test']
  headlineVariant.value = variants[Math.floor(Math.random() * variants.length)]
  ctaVariant.value = ctaVariants[Math.floor(Math.random() * ctaVariants.length)]

  // Start testimonial rotation
  testimonialInterval.value = setInterval(rotateTestimonials, 4000)

  // Simulate dynamic signup counter
  setInterval(() => {
    if (Math.random() > 0.7) { // 30% chance every 10 seconds
      todaySignups.value += 1
    }
  }, 10000)

  // Start Berner Rösti card animations
  setTimeout(() => {
    startCardAnimations.value = true
  }, 100)

  // Start typing effect for recipe title
  let charIndex = 0
  displayedTitle.value = ''

  clearInterval(typingInterval)
  clearInterval(typingCursorInterval)
  document.documentElement.style.removeProperty('--typing-cursor-opacity')

  typingInterval = setInterval(() => {
    if (charIndex < targetTitle.value.length) {
      displayedTitle.value += targetTitle.value.charAt(charIndex)
      charIndex++
    } else {
      clearInterval(typingInterval)
      // Start cursor blinking after typing finishes
      let cursorVisible = true
      typingCursorInterval = setInterval(() => {
        cursorVisible = !cursorVisible
        document.documentElement.style.setProperty('--typing-cursor-opacity', cursorVisible ? '1' : '0')
      }, 500)
    }
  }, 70)
})

onUnmounted(() => {
  if (testimonialInterval.value) {
    clearInterval(testimonialInterval.value)
  }
  clearInterval(typingInterval)
  clearInterval(typingCursorInterval)
  document.documentElement.style.removeProperty('--typing-cursor-opacity')
})
</script>

<style scoped>
/* Custom animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.6s ease-out;
}

/* Berner Rösti Card Animations */
@keyframes pop-background {
  0% { transform: scale(0.95); opacity: 0.8; }
  50% { transform: scale(1.05); opacity: 1; }
  100% { transform: scale(1); opacity: 1; }
}

@keyframes jumpAnimation {
  0% { transform: translateY(10px) scale(0.9); opacity: 0; }
  70% { transform: translateY(-5px) scale(1.05); opacity: 1; }
  100% { transform: translateY(0) scale(1); opacity: 1; }
}

@keyframes spinFast {
  from { transform: rotate(0deg); }
  to { transform: rotate(720deg); }
}

@keyframes revealButtonText {
  from { width: 0; }
  to { width: 100%; }
}

/* Typing cursor for title */
.title-element::after {
  content: '|';
  position: absolute;
  right: -8px;
  top: 0;
  opacity: var(--typing-cursor-opacity, 0);
  animation: blink 1s step-end infinite;
  font-size: inherit;
  line-height: inherit;
}

@keyframes blink {
  from, to { opacity: 1; }
  50% { opacity: 0; }
}

/* Animation trigger classes for card */
.card-animations-active {
  animation: pop-background 0.4s ease-out forwards;
}

.card-animations-active .footer-button-1 {
  animation: jumpAnimation 0.6s ease-out forwards 0.5s;
  opacity: 0;
}

.card-animations-active .footer-button-2 {
  animation: jumpAnimation 0.6s ease-out forwards 0.8s;
  opacity: 0;
}

.card-animations-active .footer-button-3 {
  animation: jumpAnimation 0.6s ease-out forwards 1.1s;
  opacity: 0;
}

.card-animations-active .reload-icon-img {
  animation: spinFast 1.2s cubic-bezier(0.68, -0.55, 0.27, 1.55) forwards 0.3s;
}

.button-typing-text {
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  width: 0;
  vertical-align: top;
}

.card-animations-active .footer-button-2 .button-typing-text {
  animation: revealButtonText 0.4s steps(10, end) forwards 0.9s;
}

/* Card border effect */
.menu-card-copy::before {
   content: "";
   position: absolute;
   z-index: 1;
   pointer-events: none;
   top: -3px;
   left: -3px;
   right: -3px;
   bottom: -3px;
   border-radius: inherit;
   background: linear-gradient(135deg,
                transparent 0%,
                transparent 80%,
                rgba(220, 175, 90, 0.5) 95%,
                #f0d9a3 100%);
}

/* Shiny purple button effect */
.shiny-purple-button {
  position: relative;
  overflow: hidden;
}

.shiny-purple-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: inherit;
  background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.3) 50%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease-out;
  z-index: 0;
  pointer-events: none;
}

.shiny-purple-button:hover::before {
  opacity: 1;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}
</style>
