<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Navigation -->
    <nav class="fixed top-0 w-full bg-white/95 backdrop-blur-md z-50 shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-20">
          <div class="flex items-center">
            <img src="/src/assets/ordy_logo.svg" alt="Ordy" class="h-12 w-auto">
            <span class="ml-3 text-2xl font-YesevaOne text-gray-900">Ordy</span>
          </div>
          <div class="hidden md:flex items-center space-x-8">
            <router-link to="/new-homepage" class="text-gray-700 hover:text-ordypurple-100 transition-colors font-medium">Startseite</router-link>
            <router-link to="/rezepte" class="text-ordypurple-100 font-medium">Rezepte</router-link>
            <router-link to="/blog" class="text-gray-700 hover:text-ordypurple-100 transition-colors font-medium">Blog</router-link>
            <router-link to="/about" class="text-gray-700 hover:text-ordypurple-100 transition-colors font-medium">Über uns</router-link>
            <router-link to="/login" class="bg-ordypurple-100 text-white px-6 py-3 rounded-full hover:bg-ordypurple-200 transition-all transform hover:scale-105 font-medium shadow-lg">
              Kostenlos starten
            </router-link>
          </div>
          <button @click="toggleMobileMenu" class="md:hidden p-2">
            <svg class="h-6 w-6 text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>
      </div>

      <!-- Mobile Menu -->
      <div v-if="mobileMenuOpen" class="md:hidden bg-white border-t border-gray-200">
        <div class="px-4 py-6 space-y-4">
          <router-link to="/new-homepage" @click="mobileMenuOpen = false" class="block text-gray-700 hover:text-ordypurple-100 transition-colors font-medium">Startseite</router-link>
          <router-link to="/rezepte" @click="mobileMenuOpen = false" class="block text-ordypurple-100 font-medium">Rezepte</router-link>
          <router-link to="/blog" @click="mobileMenuOpen = false" class="block text-gray-700 hover:text-ordypurple-100 transition-colors font-medium">Blog</router-link>
          <router-link to="/about" @click="mobileMenuOpen = false" class="block text-gray-700 hover:text-ordypurple-100 transition-colors font-medium">Über uns</router-link>
          <router-link to="/login" @click="mobileMenuOpen = false" class="w-full bg-ordypurple-100 text-white px-6 py-3 rounded-full hover:bg-ordypurple-200 transition-all font-medium text-center block">
            Kostenlos starten
          </router-link>
        </div>
      </div>
    </nav>

    <!-- Header -->
    <header class="pt-32 pb-16 bg-gradient-to-br from-white via-violet-100 to-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-4xl md:text-6xl font-YesevaOne text-gray-900 mb-4">
          Kostenlose <span class="text-ordypurple-100">Rezepte</span> entdecken
        </h1>
        <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto mb-8">
          Entdecke hunderte kostenlose Rezepte aus unserer Community - ohne Anmeldung und völlig gratis
        </p>

        <!-- Quick Search -->
        <div class="max-w-2xl mx-auto">
          <div class="relative">
            <input v-model="searchQuery"
                   type="text"
                   placeholder="Nach Rezepten oder Zutaten suchen..."
                   class="w-full px-6 py-4 text-lg border border-gray-300 rounded-full focus:ring-2 focus:ring-ordypurple-100 focus:border-transparent shadow-lg">
            <button class="absolute right-2 top-2 bg-ordypurple-100 text-white p-3 rounded-full hover:bg-ordypurple-200 transition-colors">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </header>

    <!-- Quick Filters -->
    <section class="py-8 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-wrap gap-3 justify-center mb-8">
          <button v-for="ingredient in popularIngredients"
                  :key="ingredient"
                  @click="toggleIngredient(ingredient)"
                  :class="[
                    'px-4 py-2 rounded-full text-sm font-medium transition-all transform hover:scale-105',
                    selectedIngredients.includes(ingredient)
                      ? 'bg-ordypurple-100 text-white shadow-lg'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  ]">
            {{ ingredient }}
          </button>
        </div>

        <!-- Advanced Filters (Collapsible) -->
        <div class="text-center">
          <button @click="showAdvancedFilters = !showAdvancedFilters"
                  class="text-ordypurple-100 hover:text-ordypurple-200 font-medium">
            {{ showAdvancedFilters ? 'Weniger Filter' : 'Mehr Filter' }}
            <svg :class="['w-4 h-4 inline ml-1 transition-transform', showAdvancedFilters ? 'rotate-180' : '']"
                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
            </svg>
          </button>
        </div>

        <div v-if="showAdvancedFilters" class="mt-6 grid md:grid-cols-3 gap-4 max-w-4xl mx-auto">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Zubereitungszeit</label>
            <select v-model="selectedTime"
                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-ordypurple-100 focus:border-transparent">
              <option value="">Alle</option>
              <option value="quick">< 30 Min</option>
              <option value="medium">30-60 Min</option>
              <option value="long">> 60 Min</option>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Portionen</label>
            <select v-model="selectedServings"
                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-ordypurple-100 focus:border-transparent">
              <option value="">Alle</option>
              <option value="1-2">1-2 Personen</option>
              <option value="3-4">3-4 Personen</option>
              <option value="5+">5+ Personen</option>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Sortierung</label>
            <select v-model="sortBy"
                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-ordypurple-100 focus:border-transparent">
              <option value="newest">Neueste zuerst</option>
              <option value="name">Alphabetisch</option>
              <option value="time">Nach Zubereitungszeit</option>
            </select>
          </div>
        </div>
      </div>
    </section>

    <!-- Recipe Results -->
    <section class="py-8 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Results Header -->
        <div class="flex items-center justify-between mb-8">
          <div>
            <h2 class="text-2xl md:text-3xl font-YesevaOne text-gray-900">
              {{ isLoading ? 'Lade Rezepte...' : `${filteredRecipes.length} kostenlose Rezepte` }}
            </h2>
            <p class="text-gray-600 mt-1">
              {{ selectedIngredients.length > 0 ? `Mit: ${selectedIngredients.join(', ')}` : 'Alle verfügbaren Rezepte' }}
            </p>
          </div>
        </div>

        <!-- Loading State -->
        <div v-if="isLoading" class="flex justify-center items-center py-16">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-ordypurple-100"></div>
        </div>

        <!-- No Results -->
        <div v-else-if="filteredRecipes.length === 0" class="text-center py-16">
          <div class="text-6xl mb-4">🍳</div>
          <h3 class="text-xl font-YesevaOne text-gray-900 mb-2">Keine Rezepte gefunden</h3>
          <p class="text-gray-600 mb-6">Versuche andere Suchbegriffe oder entferne einige Filter</p>
          <button @click="clearAllFilters" class="bg-ordypurple-100 text-white px-6 py-3 rounded-full hover:bg-ordypurple-200 transition-colors">
            Alle Filter zurücksetzen
          </button>
        </div>

        <!-- Recipe Grid with MenuCard -->
        <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8 justify-items-center">
          <MenuCard
            v-for="(recipe, index) in paginatedRecipes"
            :key="recipe._id"
            :element="recipe"
            :index="index"
            :menuPlanType="{ addToMenu: false, editMenu: false, none: true }"
            :weekplanParentData="{ plannedSeats: null }"
            class="transform hover:scale-105 transition-transform duration-200"
          />
        </div>

        <!-- Pagination -->
        <div v-if="totalPages > 1" class="flex items-center justify-center mt-12 space-x-2">
          <button @click="currentPage = Math.max(1, currentPage - 1)"
                  :disabled="currentPage === 1"
                  class="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
            ← Zurück
          </button>

          <span v-for="page in visiblePages"
                :key="page"
                @click="currentPage = page"
                :class="[
                  'px-4 py-2 rounded-lg cursor-pointer transition-colors',
                  page === currentPage
                    ? 'bg-ordypurple-100 text-white'
                    : 'border border-gray-300 hover:bg-gray-50'
                ]">
            {{ page }}
          </span>

          <button @click="currentPage = Math.min(totalPages, currentPage + 1)"
                  :disabled="currentPage === totalPages"
                  class="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
            Weiter →
          </button>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="py-16 bg-gradient-to-br from-ordypurple-100 to-ordypurple-200">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl md:text-4xl font-YesevaOne text-white mb-4">
          Lust auf mehr?
        </h2>
        <p class="text-lg md:text-xl text-white/90 mb-8">
          Mit Ordy bekommst du personalisierte KI-Rezepte, automatische Wochenplanung, intelligente Einkaufslisten und vieles mehr
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <router-link to="/login"
                       class="inline-block bg-white text-ordypurple-100 px-8 py-4 rounded-full text-lg font-medium hover:bg-gray-50 transition-all transform hover:scale-105 shadow-lg">
            Kostenlos anmelden
          </router-link>
          <router-link to="/new-homepage"
                       class="inline-block border-2 border-white text-white px-8 py-4 rounded-full text-lg font-medium hover:bg-white hover:text-ordypurple-100 transition-all transform hover:scale-105">
            Mehr erfahren
          </router-link>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import axios from 'axios'
import MenuCard from '../components/menuCard.vue'

const router = useRouter()

// State
const searchQuery = ref('')
const selectedTime = ref('')
const selectedServings = ref('')
const selectedIngredients = ref([])
const showAdvancedFilters = ref(false)
const sortBy = ref('newest')
const currentPage = ref(1)
const recipesPerPage = 12
const isLoading = ref(true)
const mobileMenuOpen = ref(false)

// Data
const recipes = ref([])
const allRecipes = ref([])

const popularIngredients = [
  'Hähnchen', 'Nudeln', 'Tomaten', 'Zwiebeln', 'Käse', 'Eier',
  'Kartoffeln', 'Paprika', 'Spinat', 'Reis', 'Knoblauch', 'Karotten',
  'Brokkoli', 'Zucchini', 'Pilze', 'Lachs'
]

// API Functions - Using existing endpoints
const loadFreeAccessRecipes = async () => {
  try {
    isLoading.value = true

    // For now, use mock data since the API requires authentication
    // In a real implementation, this would use a public endpoint
    generateMockRecipes()

    // TODO: Implement public API endpoint for free access recipes
    // const response = await axios.get(`${import.meta.env.VITE_API_BASE_URL}/api/v1/menu/complete/all/freeaccess`)

  } catch (error) {
    console.error('Error loading recipes:', error)
    // Fallback to mock data
    generateMockRecipes()
  } finally {
    isLoading.value = false
  }
}

// Generate mock recipe data that looks like real MenuCard data
const generateMockRecipes = () => {
  const mockRecipes = [
    {
      _id: '1',
      name: 'Berner Rösti',
      description: 'Traditionelle Schweizer Rösti mit knuspriger Kruste',
      imagelink: '/src/assets/img/_bernerroesti.png',
      numberOfPersons: 4,
      cookingTime: 25,
      freeAccess: true,
      ingredients: [
        { name: 'Kartoffeln' },
        { name: 'Butter' },
        { name: 'Salz' }
      ],
      createdAt: new Date('2024-11-15')
    },
    {
      _id: '2',
      name: 'Mediterrane Gemüse-Pfanne',
      description: 'Bunte Gemüsepfanne mit mediterranen Kräutern',
      imagelink: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=300&fit=crop',
      numberOfPersons: 3,
      cookingTime: 20,
      freeAccess: true,
      ingredients: [
        { name: 'Zucchini' },
        { name: 'Paprika' },
        { name: 'Tomaten' },
        { name: 'Zwiebeln' }
      ],
      createdAt: new Date('2024-11-14')
    },
    {
      _id: '3',
      name: 'Cremige Spinat-Käse-Omelette',
      description: 'Fluffiges Omelette mit frischem Spinat und Käse',
      imagelink: 'https://images.unsplash.com/photo-1506084868230-bb9d95c24759?w=400&h=300&fit=crop',
      numberOfPersons: 2,
      cookingTime: 15,
      freeAccess: true,
      ingredients: [
        { name: 'Eier' },
        { name: 'Spinat' },
        { name: 'Käse' },
        { name: 'Milch' }
      ],
      createdAt: new Date('2024-11-13')
    },
    {
      _id: '4',
      name: 'Hähnchen-Curry mit Reis',
      description: 'Würziges Curry mit zartem Hähnchen und Basmatireis',
      imagelink: 'https://images.unsplash.com/photo-1565299507177-b0ac66763828?w=400&h=300&fit=crop',
      numberOfPersons: 4,
      cookingTime: 35,
      freeAccess: true,
      ingredients: [
        { name: 'Hähnchen' },
        { name: 'Reis' },
        { name: 'Kokosmilch' },
        { name: 'Curry' }
      ],
      createdAt: new Date('2024-11-12')
    },
    {
      _id: '5',
      name: 'Pasta Carbonara',
      description: 'Klassische italienische Pasta mit cremiger Sauce',
      imagelink: 'https://images.unsplash.com/photo-1621996346565-e3dbc353d2e5?w=400&h=300&fit=crop',
      numberOfPersons: 3,
      cookingTime: 18,
      freeAccess: true,
      ingredients: [
        { name: 'Nudeln' },
        { name: 'Eier' },
        { name: 'Speck' },
        { name: 'Parmesan' }
      ],
      createdAt: new Date('2024-11-11')
    },
    {
      _id: '6',
      name: 'Vegetarische Lasagne',
      description: 'Schichtlasagne mit Gemüse und Béchamelsauce',
      imagelink: 'https://images.unsplash.com/photo-1574894709920-11b28e7367e3?w=400&h=300&fit=crop',
      numberOfPersons: 6,
      cookingTime: 75,
      freeAccess: true,
      ingredients: [
        { name: 'Lasagneplatten' },
        { name: 'Zucchini' },
        { name: 'Aubergine' },
        { name: 'Mozzarella' }
      ],
      createdAt: new Date('2024-11-10')
    }
  ]

  allRecipes.value = mockRecipes
  recipes.value = allRecipes.value
}

// Computed properties
const filteredRecipes = computed(() => {
  let filtered = [...allRecipes.value]

  // Search filter
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(recipe =>
      recipe.name?.toLowerCase().includes(query) ||
      recipe.description?.toLowerCase().includes(query) ||
      recipe.ingredients?.some(ing => ing.name?.toLowerCase().includes(query))
    )
  }

  // Time filter
  if (selectedTime.value) {
    filtered = filtered.filter(recipe => {
      const time = recipe.cookingTime || 30
      switch (selectedTime.value) {
        case 'quick': return time < 30
        case 'medium': return time >= 30 && time <= 60
        case 'long': return time > 60
        default: return true
      }
    })
  }

  // Servings filter
  if (selectedServings.value) {
    filtered = filtered.filter(recipe => {
      const servings = recipe.numberOfPersons || recipe.seatCount || 4
      switch (selectedServings.value) {
        case '1-2': return servings <= 2
        case '3-4': return servings >= 3 && servings <= 4
        case '5+': return servings >= 5
        default: return true
      }
    })
  }

  // Ingredients filter
  if (selectedIngredients.value.length > 0) {
    filtered = filtered.filter(recipe =>
      selectedIngredients.value.some(ingredient =>
        recipe.ingredients?.some(recipeIngredient =>
          recipeIngredient.name?.toLowerCase().includes(ingredient.toLowerCase())
        ) ||
        recipe.name?.toLowerCase().includes(ingredient.toLowerCase())
      )
    )
  }

  // Apply sorting
  switch (sortBy.value) {
    case 'name':
      filtered.sort((a, b) => (a.name || '').localeCompare(b.name || ''))
      break
    case 'time':
      filtered.sort((a, b) => (a.cookingTime || 30) - (b.cookingTime || 30))
      break
    case 'newest':
    default:
      filtered.sort((a, b) => new Date(b.createdAt || b.created || 0) - new Date(a.createdAt || a.created || 0))
  }

  return filtered
})

const totalPages = computed(() => Math.ceil(filteredRecipes.value.length / recipesPerPage))

const paginatedRecipes = computed(() => {
  const start = (currentPage.value - 1) * recipesPerPage
  const end = start + recipesPerPage
  return filteredRecipes.value.slice(start, end)
})

const visiblePages = computed(() => {
  const pages = []
  const start = Math.max(1, currentPage.value - 2)
  const end = Math.min(totalPages.value, start + 4)
  
  for (let i = start; i <= end; i++) {
    pages.push(i)
  }
  
  return pages
})

// Methods
const toggleIngredient = (ingredient) => {
  const index = selectedIngredients.value.indexOf(ingredient)
  if (index > -1) {
    selectedIngredients.value.splice(index, 1)
  } else {
    selectedIngredients.value.push(ingredient)
  }
}

const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value
}

const clearAllFilters = () => {
  searchQuery.value = ''
  selectedTime.value = ''
  selectedServings.value = ''
  selectedIngredients.value = []
  currentPage.value = 1
}

// Watch for search changes
watch(searchQuery, () => {
  currentPage.value = 1
})

watch(selectedIngredients, () => {
  currentPage.value = 1
}, { deep: true })

onMounted(async () => {
  await loadFreeAccessRecipes()
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
